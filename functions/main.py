"""
Maomao E-commerce Platform - Firebase Cloud Functions
Main entry point for all API endpoints
"""
import os
import time
import logging
from flask import Flask, jsonify, request
from flask_cors import CORS
from firebase_functions import https_fn
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Configure CORS
from config.cors_config import CORSConfig
CORSConfig.configure_cors(app)

# Initialize Firebase
from config.firebase_config import FirebaseConfig
try:
    FirebaseConfig.initialize()
    logger.info("Firebase initialized successfully")
except Exception as e:
    logger.error(f"Firebase initialization failed: {str(e)}")

# Initialize Algolia
from config.algolia_config import AlgoliaConfig
try:
    AlgoliaConfig.initialize()
    logger.info("Algolia initialized successfully")
except Exception as e:
    logger.warning(f"Algolia initialization failed: {str(e)}")

# Import and register API routes
from api.product_api import product_bp
from api.order_api import order_bp
from api.user_api import user_bp
from api.groupbuy_api import groupbuy_bp
from api.search_api import search_bp
from api.admin_api import admin_bp

# Register blueprints
app.register_blueprint(product_bp, url_prefix='/api')
app.register_blueprint(order_bp, url_prefix='/api')
app.register_blueprint(user_bp, url_prefix='/api')
app.register_blueprint(groupbuy_bp, url_prefix='/api')
app.register_blueprint(search_bp, url_prefix='/api')
app.register_blueprint(admin_bp, url_prefix='/api/admin')

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'maomao-api',
        'version': '1.0.0',
        'timestamp': int(time.time())
    })

# Root endpoint
@app.route('/')
def root():
    """Root endpoint"""
    return jsonify({
        'message': 'Maomao E-commerce API',
        'version': '1.0.0',
        'documentation': '/api/docs',
        'health': '/health'
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': {
            'code': 'NOT_FOUND',
            'message': 'Endpoint not found'
        }
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors"""
    return jsonify({
        'success': False,
        'error': {
            'code': 'METHOD_NOT_ALLOWED',
            'message': 'Method not allowed for this endpoint'
        }
    }), 405

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        'success': False,
        'error': {
            'code': 'INTERNAL_ERROR',
            'message': 'Internal server error'
        }
    }), 500

# Request logging middleware
@app.before_request
def log_request():
    """Log incoming requests"""
    logger.info(f"{request.method} {request.path} - {request.remote_addr}")

# Response headers middleware
@app.after_request
def after_request(response):
    """Add common response headers"""
    response.headers['X-API-Version'] = '1.0.0'
    response.headers['X-Service'] = 'maomao-api'
    return response

# Export the Flask app as a Cloud Function
@https_fn.on_request(cors=True)
def api(req: https_fn.Request) -> https_fn.Response:
    """Main API Cloud Function"""
    with app.request_context(req.environ):
        return app.full_dispatch_request()