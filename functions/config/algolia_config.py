"""
Algolia search configuration and client management
"""
import os
import logging
from typing import Optional, Dict, Any, List
from algoliasearch.search_client import SearchClient
from algoliasearch.search_index import SearchIndex

logger = logging.getLogger(__name__)

class AlgoliaConfig:
    """Algolia search configuration and client management"""
    
    _client: Optional[SearchClient] = None
    _indices: Dict[str, SearchIndex] = {}
    
    @classmethod
    def initialize(cls) -> None:
        """Initialize Algolia search client"""
        if cls._client is not None:
            logger.info("Algolia already initialized")
            return
            
        try:
            app_id = os.getenv('ALGOLIA_APP_ID')
            api_key = os.getenv('ALGOLIA_API_KEY')
            
            if not app_id or not api_key:
                raise ValueError("Missing Algolia configuration environment variables")
            
            cls._client = SearchClient.create(app_id, api_key)
            
            # Initialize indices
            products_index = os.getenv('ALGOLIA_PRODUCTS_INDEX', 'products')
            categories_index = os.getenv('ALGOLIA_CATEGORIES_INDEX', 'categories')
            
            cls._indices['products'] = cls._client.init_index(products_index)
            cls._indices['categories'] = cls._client.init_index(categories_index)
            
            logger.info("Algolia client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Algolia: {str(e)}")
            raise
    
    @classmethod
    def get_client(cls) -> SearchClient:
        """Get Algolia search client"""
        if cls._client is None:
            cls.initialize()
        return cls._client
    
    @classmethod
    def get_index(cls, index_name: str) -> SearchIndex:
        """Get Algolia search index"""
        if cls._client is None:
            cls.initialize()
        
        if index_name not in cls._indices:
            cls._indices[index_name] = cls._client.init_index(index_name)
        
        return cls._indices[index_name]
    
    @classmethod
    def get_products_index(cls) -> SearchIndex:
        """Get products search index"""
        return cls.get_index('products')
    
    @classmethod
    def get_categories_index(cls) -> SearchIndex:
        """Get categories search index"""
        return cls.get_index('categories')
    
    @classmethod
    def index_product(cls, product_data: Dict[str, Any]) -> None:
        """Index a single product"""
        try:
            products_index = cls.get_products_index()
            
            # Prepare product data for indexing
            search_data = cls._prepare_product_for_search(product_data)
            
            products_index.save_object(search_data)
            logger.info(f"Product {product_data.get('id')} indexed successfully")
            
        except Exception as e:
            logger.error(f"Failed to index product {product_data.get('id')}: {str(e)}")
            raise
    
    @classmethod
    def index_products_batch(cls, products: List[Dict[str, Any]]) -> None:
        """Index multiple products in batch"""
        try:
            products_index = cls.get_products_index()
            
            # Prepare products data for indexing
            search_data = [cls._prepare_product_for_search(product) for product in products]
            
            products_index.save_objects(search_data)
            logger.info(f"Batch indexed {len(products)} products successfully")
            
        except Exception as e:
            logger.error(f"Failed to batch index products: {str(e)}")
            raise
    
    @classmethod
    def delete_product(cls, product_id: str) -> None:
        """Delete product from search index"""
        try:
            products_index = cls.get_products_index()
            products_index.delete_object(product_id)
            logger.info(f"Product {product_id} deleted from search index")
            
        except Exception as e:
            logger.error(f"Failed to delete product {product_id} from search: {str(e)}")
            raise
    
    @classmethod
    def search_products(cls, query: str, filters: Optional[Dict[str, Any]] = None, 
                       page: int = 0, per_page: int = 20) -> Dict[str, Any]:
        """Search products"""
        try:
            products_index = cls.get_products_index()
            
            search_params = {
                'page': page,
                'hitsPerPage': per_page,
                'attributesToRetrieve': [
                    'objectID', 'name', 'description', 'category', 
                    'coverImageUrl', 'status', 'createdAt'
                ],
                'attributesToHighlight': ['name', 'description'],
                'highlightPreTag': '<mark>',
                'highlightPostTag': '</mark>'
            }
            
            # Add filters if provided
            if filters:
                filter_strings = []
                
                if 'category' in filters:
                    filter_strings.append(f"category:'{filters['category']}'")
                
                if 'status' in filters:
                    filter_strings.append(f"status:'{filters['status']}'")
                
                if 'price_min' in filters:
                    filter_strings.append(f"price >= {filters['price_min']}")
                
                if 'price_max' in filters:
                    filter_strings.append(f"price <= {filters['price_max']}")
                
                if filter_strings:
                    search_params['filters'] = ' AND '.join(filter_strings)
            
            result = products_index.search(query, search_params)
            
            return {
                'hits': result['hits'],
                'total': result['nbHits'],
                'page': result['page'],
                'pages': result['nbPages'],
                'per_page': result['hitsPerPage'],
                'processing_time': result['processingTimeMS']
            }
            
        except Exception as e:
            logger.error(f"Product search failed: {str(e)}")
            raise
    
    @classmethod
    def get_search_suggestions(cls, query: str, limit: int = 5) -> List[str]:
        """Get search suggestions"""
        try:
            products_index = cls.get_products_index()
            
            result = products_index.search(query, {
                'hitsPerPage': limit,
                'attributesToRetrieve': ['name'],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7
            })
            
            suggestions = []
            for hit in result['hits']:
                if hit['name'] not in suggestions:
                    suggestions.append(hit['name'])
            
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Failed to get search suggestions: {str(e)}")
            return []
    
    @classmethod
    def _prepare_product_for_search(cls, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare product data for Algolia indexing"""
        return {
            'objectID': product_data.get('id'),
            'name': product_data.get('name', ''),
            'description': product_data.get('description', ''),
            'category': product_data.get('category', ''),
            'coverImageUrl': product_data.get('coverImageUrl', ''),
            'status': product_data.get('status', 'Active'),
            'createdAt': product_data.get('createdAt', 0),
            'updatedAt': product_data.get('updatedAt', 0),
            # Add searchable text for better matching
            '_searchable_text': f"{product_data.get('name', '')} {product_data.get('description', '')} {product_data.get('category', '')}".lower()
        }
    
    @classmethod
    def configure_index_settings(cls) -> None:
        """Configure Algolia index settings"""
        try:
            products_index = cls.get_products_index()
            
            settings = {
                'searchableAttributes': [
                    'name',
                    'description',
                    'category',
                    '_searchable_text'
                ],
                'attributesForFaceting': [
                    'category',
                    'status'
                ],
                'customRanking': [
                    'desc(createdAt)'
                ],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7,
                'allowTyposOnNumericTokens': False,
                'ignorePlurals': True,
                'removeStopWords': True
            }
            
            products_index.set_settings(settings)
            logger.info("Algolia index settings configured successfully")
            
        except Exception as e:
            logger.error(f"Failed to configure Algolia index settings: {str(e)}")
            raise

# Initialize Algolia on module import (optional, can fail silently)
try:
    AlgoliaConfig.initialize()
    AlgoliaConfig.configure_index_settings()
except Exception as e:
    logger.warning(f"Algolia initialization failed on import: {str(e)}")
    # Don't raise here to allow for manual initialization
