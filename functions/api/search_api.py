"""
Search API endpoints - Placeholder
"""
import logging
from flask import Blueprint, jsonify
from utils.formatters import success_response

logger = logging.getLogger(__name__)

# Create Blueprint
search_bp = Blueprint('search', __name__)

@search_bp.route('/search', methods=['GET'])
def search():
    """Search - Placeholder"""
    return success_response(
        data=[],
        message="Search endpoints coming soon"
    )
