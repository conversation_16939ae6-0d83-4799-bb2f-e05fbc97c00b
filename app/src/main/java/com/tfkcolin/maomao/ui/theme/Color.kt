package com.tfkcolin.maomao.ui.theme

import androidx.compose.ui.graphics.Color

// Pinduoduo-inspired color palette

// Light Theme Colors
val PinduoduoRed = Color(0xFFE02E24)
val PinduoduoRedDark = Color(0xFFC41F16)
val PinduoduoYellow = Color(0xFFFFD100)
val PinduoduoOrange = Color(0xFFFF8C00)

// Background and surface colors
val LightBackground = Color(0xFFF5F5F5)
val LightSurface = Color(0xFFFFFFFF)

// Text colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextTertiary = Color(0xFF9E9E9E)

// Dark Theme Colors
val PinduoduoRedLight = Color(0xFFFF6B61)
val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)

// Legacy colors (keeping for backward compatibility)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)